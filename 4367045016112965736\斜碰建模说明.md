# 二维斜碰建模说明文档

## 项目概述

本项目在原有一维正碰模型的基础上，扩展实现了二维斜碰的物理建模。通过引入角度参数，模拟质点在二维平面内的斜向碰撞过程，包括完全弹性、非弹性和完全非弹性三种碰撞类型。

## 物理原理分析

### 1. 正碰与斜碰的区别

**正碰（一维碰撞）**：
- 两质点的速度方向沿着连心线
- 碰撞角度为0°或180°
- 只需考虑一个方向的速度分量

**斜碰（二维碰撞）**：
- 质点的速度方向与连心线有夹角
- 需要将速度分解为法向和切向分量
- 碰撞只影响法向分量，切向分量保持不变

### 2. 斜碰的关键概念

#### 连心线（Line of Centers）
- 定义：碰撞瞬间两质点中心的连线
- 作用：确定碰撞的主要方向
- 计算：从质点2指向质点1的向量

#### 法向分量（Normal Component）
- 定义：速度在连心线方向的投影
- 特点：参与碰撞计算
- 物理意义：决定碰撞的强度和效果

#### 切向分量（Tangential Component）
- 定义：速度在垂直连心线方向的投影
- 特点：碰撞过程中保持不变
- 物理意义：不受碰撞影响的运动分量

### 3. 数学模型推导

#### 坐标系建立
使用二维笛卡尔坐标系(x, y)，质点位置用(x₁, y₁)和(x₂, y₂)表示。

#### 连心线单位向量计算
```
dx = x₁ - x₂
dy = y₁ - y₂
distance = √(dx² + dy²)

法向单位向量：n⃗ = (dx/distance, dy/distance)
切向单位向量：t⃗ = (-dy/distance, dx/distance)
```

#### 速度分解
对于质点i的速度v⃗ᵢ = (vxᵢ, vyᵢ)：
```
法向分量：vᵢₙ = v⃗ᵢ · n⃗ = vxᵢ × nx + vyᵢ × ny
切向分量：vᵢₜ = v⃗ᵢ · t⃗ = vxᵢ × tx + vyᵢ × ty
```

#### 碰撞计算
只对法向分量应用一维碰撞公式：

**完全弹性碰撞**：
```
v₁ₙ' = [(m₁-m₂)v₁ₙ + 2m₂v₂ₙ] / (m₁+m₂)
v₂ₙ' = [2m₁v₁ₙ + (m₂-m₁)v₂ₙ] / (m₁+m₂)
```

**非弹性碰撞**：
```
v₁ₙ' = [(m₁-m₂)v₁ₙ + 2m₂v₂ₙ] / (m₁+m₂) × e
v₂ₙ' = [2m₁v₁ₙ + (m₂-m₁)v₂ₙ] / (m₁+m₂) × e
```
其中e为恢复系数(0 < e ≤ 1)

**完全非弹性碰撞**：
```
v₁ₙ' = v₂ₙ' = (m₁v₁ₙ + m₂v₂ₙ) / (m₁+m₂)
```

#### 速度合成
将计算后的法向分量和原切向分量合成新的速度向量：
```
v⃗₁' = v₁ₙ' × n⃗ + v₁ₜ × t⃗
v⃗₂' = v₂ₙ' × n⃗ + v₂ₜ × t⃗
```

## 代码实现结构

### 类设计：ObliqueCollisionSimulator

#### 主要属性
- `m1, m2`: 质点质量
- `dt`: 时间步长
- `total_time`: 总模拟时间
- `radius`: 质点半径（用于碰撞检测）
- 位置和速度存储数组

#### 核心方法

1. **set_initial_conditions()**: 设置初始条件
2. **check_collision()**: 碰撞检测
3. **elastic_collision()**: 完全弹性碰撞计算
4. **inelastic_collision()**: 非弹性碰撞计算
5. **completely_inelastic_collision()**: 完全非弹性碰撞计算
6. **simulate()**: 运行模拟
7. **create_animation()**: 创建动画

### 关键算法实现

#### 碰撞检测
```python
def check_collision(self):
    distance = math.sqrt((self.x1 - self.x2)**2 + (self.y1 - self.y2)**2)
    return distance <= 2 * self.radius
```

#### 速度分解与合成
```python
# 连心线方向
dx = self.x1 - self.x2
dy = self.y1 - self.y2
distance = math.sqrt(dx**2 + dy**2)

# 单位向量
nx = dx / distance
ny = dy / distance
tx = -ny
ty = nx

# 速度分解
v1n = self.vx1 * nx + self.vy1 * ny
v1t = self.vx1 * tx + self.vy1 * ty

# 速度合成
self.vx1 = v1n_new * nx + v1t_new * tx
self.vy1 = v1n_new * ny + v1t_new * ty
```

## 使用方法

### 基本使用
```python
# 创建模拟器
sim = ObliqueCollisionSimulator(m1=1.0, m2=1.5, dt=0.01, total_time=3.0)

# 设置初始条件
sim.set_initial_conditions(
    x1=0.0, y1=0.0, vx1=1.2, vy1=0.8,
    x2=1.8, y2=0.5, vx2=-0.9, vy2=-0.3
)

# 运行模拟
sim.simulate('elastic')  # 'elastic', 'inelastic', 'completely_inelastic'

# 创建动画
ani = sim.create_animation("斜碰模拟")

# 在Jupyter中显示
HTML(ani.to_jshtml())
```

### 对比演示
```python
# 创建三种碰撞类型的对比动画
comparison_ani = create_comparison_animation()
HTML(comparison_ani.to_jshtml())
```

## 与原有正碰模型的对比

| 特性 | 正碰模型 | 斜碰模型 |
|------|----------|----------|
| 维度 | 一维 | 二维 |
| 速度处理 | 直接计算 | 分解为法向和切向分量 |
| 碰撞计算 | 应用于整个速度 | 仅应用于法向分量 |
| 角度考虑 | 固定为0°或180° | 任意角度 |
| 复杂度 | 简单 | 中等 |
| 物理真实性 | 特殊情况 | 更接近现实 |

## 物理验证

### 守恒定律验证
1. **动量守恒**：系统总动量在碰撞前后保持不变
2. **能量关系**：
   - 完全弹性：动能守恒
   - 非弹性：动能部分损失
   - 完全非弹性：动能损失最大

### 边界情况验证
1. **正碰情况**：当初始速度沿连心线时，退化为一维正碰
2. **质量相等**：简化的碰撞公式
3. **静止碰撞**：一个质点静止时的特殊情况

## 扩展可能性

1. **多质点系统**：扩展到多个质点的碰撞
2. **边界条件**：添加墙壁等边界约束
3. **摩擦力**：考虑表面摩擦的影响
4. **旋转运动**：添加角动量和转动惯量
5. **三维扩展**：扩展到三维空间

## 应用场景

1. **教学演示**：物理课程中的碰撞理论教学
2. **游戏开发**：游戏中的物理引擎
3. **工程分析**：机械碰撞分析
4. **科研模拟**：粒子物理研究

## 总结

本斜碰建模成功地将一维正碰扩展到二维斜碰，通过速度分解的方法巧妙地处理了角度问题。模型保持了物理的准确性，同时提供了直观的可视化效果，为理解碰撞物理提供了有力的工具。
