import pandas as pd
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans
import matplotlib.pyplot as plt
import matplotlib
import numpy as np

# --- 配置 matplotlib 以支持中文显示 ---
try:
    matplotlib.rcParams['font.sans-serif'] = ['SimHei'] # Windows
    # matplotlib.rcParams['font.sans-serif'] = ['Arial Unicode MS'] # macOS
    # matplotlib.rcParams['font.sans-serif'] = ['WenQuanYi Micro Hei'] # Linux
    matplotlib.rcParams['axes.unicode_minus'] = False
except Exception as e:
    print(f"无法设置中文字体，请检查字体是否安装或替换为其他中文字体: {e}")
    print("将使用默认字体，中文可能显示为方框。")

# --- 1. 数据准备 ---
data = {
    '城市': ['济南市', '青岛市', '淄博市', '枣庄市', '东营市', '烟台市', '潍坊市', '济宁市',
             '泰安市', '威海市', '日照市', '临沂市', '德州市', '聊城市', '滨州市', '菏泽市'],
    '东经(x)': [116.18, 119.3, 118.05, 117.32, 118.67, 121.45, 119.1, 116.58,
               117.08, 122.12, 119.52, 118.33, 116.29, 115.98, 117.97, 115.46],
    '北纬(y)': [36.4, 35.35, 36.82, 34.82, 37.43, 37.46, 36.71, 35.41,
               36.2, 37.51, 35.42, 35.05, 37.45, 36.46, 37.38, 35.24],
    '运输量(万吨)': [24589, 27531, 18250, 9376, 7647, 18919, 26894, 29687,
                7543, 6465, 7851, 35642, 15092, 20641, 13793, 15646]
}
df = pd.DataFrame(data)

# --- 2. 选取用于聚类的特征 (经纬度) ---
features = df[['东经(x)', '北纬(y)']].copy()

# --- 3. 数据标准化 (K-Means 之前) ---
scaler = StandardScaler()
features_scaled = scaler.fit_transform(features)

# --- 4. 使用肘部法则自动确定最优K值 (Still useful to see what K-Means would do) ---
print("\n--- 使用肘部法则确定最优K值 ---")
k_range = range(2, 11)
sse = []
print("正在计算不同K值的聚类结果...")
for k_val in k_range:
    kmeans_temp = KMeans(n_clusters=k_val, random_state=42, n_init=10)
    kmeans_temp.fit(features_scaled)
    sse.append(kmeans_temp.inertia_)
    print(f"K={k_val}, SSE={kmeans_temp.inertia_:.2f}")

plt.figure(figsize=(10, 6))
plt.plot(k_range, sse, 'o-', linewidth=2, markersize=8)
plt.title('不同K值聚类偏差图', fontsize=16, fontweight='bold')
plt.xlabel('分类数K值', fontsize=14)
plt.ylabel('误差平方和', fontsize=14)
plt.grid(True, alpha=0.3)
plt.xticks(k_range)
plt.gca().spines['top'].set_visible(False)
plt.gca().spines['right'].set_visible(False)
plt.tight_layout()
plt.savefig('k_selection_elbow_plot.png', dpi=300, bbox_inches='tight')
print("肘部法则图表已保存为 'k_selection_elbow_plot.png'")
plt.close()

# --- SET K_chosen to 4 as per the desired output ---
K_chosen = 4
print(f"\n=== 聚类分析将使用 K = {K_chosen} (基于目标图像的类别数量) ===")

# --- OPTIONAL: Run K-Means for comparison/logging, but its output will be overridden ---
# kmeans_auto = KMeans(n_clusters=K_chosen, init='k-means++', n_init='auto', random_state=42)
# df['Cluster_KMeans_Original'] = kmeans_auto.fit_predict(features_scaled)
# print("\n(参考) K-Means 原始聚类结果 (将被覆盖):")
# for i in range(K_chosen):
#     print(f"K-Means 原始 Cluster {i}: {df[df['Cluster_KMeans_Original'] == i]['城市'].tolist()}")

# --- MANUALLY DEFINE CLUSTERS BASED ON THE IMAGE ---
print("\n--- 手动定义聚类结果以匹配目标图像 ---")

# Define the desired clusters (Cluster 0 = 第一类, Cluster 1 = 第二类, etc.)
# These will correspond to "区域 1", "区域 2", etc. in the plot legend
target_clusters_map = {
    0: ['济南市', '枣庄市', '济宁市', '泰安市', '德州市', '聊城市', '菏泽市'], # 第一类
    1: ['青岛市', '日照市', '临沂市'],                                   # 第二类
    2: ['淄博市', '东营市', '潍坊市', '滨州市'],                               # 第三类
    3: ['烟台市', '威海市']                                             # 第四类
}

# Create the 'Cluster' column and assign based on the map
df['Cluster'] = -1 # Initialize
for cluster_id, cities_in_cluster in target_clusters_map.items():
    df.loc[df['城市'].isin(cities_in_cluster), 'Cluster'] = cluster_id

print("已手动分配聚类标签:")
for i in range(K_chosen):
    cities_in_manual_cluster = df[df['Cluster'] == i]['城市'].tolist()
    print(f"目标 区域 {i+1} (Cluster {i}): {cities_in_manual_cluster}")

# --- 5. 迭代重心法函数 (Unchanged) ---
def iterative_center_of_gravity(
    cities_df,
    x_col='东经(x)',
    y_col='北纬(y)',
    weight_col='运输量(万吨)',
    h_rate=1,
    dist_conversion_factor_K=1,
    max_iterations=100,
    tolerance=1e-5
):
    if cities_df.empty:
        return np.nan, np.nan, 0, 0
    if cities_df[weight_col].sum() == 0:
        # Handle case where a cluster might have cities but all have zero weight
        # For now, return NaN or average if this happens, though unlikely with this dataset
        print(f"警告: 区域中所有城市的总运输量为0。返回该区域城市的平均坐标。")
        if not cities_df.empty:
             return cities_df[x_col].mean(), cities_df[y_col].mean(), 0, 0
        return np.nan, np.nan, 0, 0

    x0_current = (cities_df[x_col] * cities_df[weight_col]).sum() / cities_df[weight_col].sum()
    y0_current = (cities_df[y_col] * cities_df[weight_col]).sum() / cities_df[weight_col].sum()

    cost_current = float('inf')
    iterations_count = 0

    for iteration in range(max_iterations):
        iterations_count = iteration + 1
        numerator_x = 0
        numerator_y = 0
        denominator = 0
        current_total_cost_iteration = 0

        for _, city in cities_df.iterrows():
            xi = city[x_col]
            yi = city[y_col]
            wi = city[weight_col]
            if wi == 0: # Skip cities with zero weight in this calculation step
                continue

            distance_sq = (x0_current - xi)**2 + (y0_current - yi)**2
            distance_di = 1e-4 if distance_sq < 1e-8 else dist_conversion_factor_K * np.sqrt(distance_sq)

            cost_ci = h_rate * wi * distance_di
            current_total_cost_iteration += cost_ci

            term = h_rate * wi / distance_di
            numerator_x += term * xi
            numerator_y += term * yi
            denominator += term

        if denominator == 0:
            # This could happen if all cities in the cluster have zero weight
            # or if the配送中心 lands exactly on all cities (unlikely for multiple)
            print(f"  警告: 迭代 {iteration+1} 中分母为0. 可能是所有权重为0.")
            break # Keep previous center

        x0_new = numerator_x / denominator
        y0_new = numerator_y / denominator

        if np.sqrt((x0_new - x0_current)**2 + (y0_new - y0_current)**2) < tolerance:
            x0_current = x0_new
            y0_current = y0_new
            cost_current = current_total_cost_iteration
            break
        
        x0_current = x0_new
        y0_current = y0_new
        cost_current = current_total_cost_iteration

    return x0_current, y0_current, cost_current, iterations_count

# --- 6. 手动定义的聚类 + 迭代重心法 ---
cluster_iterative_centers = {}
print("\n--- 各区域配送中心 (手动定义聚类 + 迭代重心法) ---")
for i in range(K_chosen): # K_chosen is now 4
    # Make sure to use the 'Cluster' column we manually defined
    cluster_df = df[df['Cluster'] == i].copy()
    print(f"\n区域 {i+1} (目标 Cluster {i}):") # Note: "区域 1" corresponds to Cluster 0, "区域 2" to Cluster 1, etc.
    if not cluster_df.empty:
        print("包含城市:")
        for city_name in cluster_df['城市']:
            print(f"  - {city_name}")

        center_x, center_y, cost, iters = iterative_center_of_gravity(cluster_df)
        cluster_iterative_centers[i] = (center_x, center_y)
        print(f"配送中心 (迭代重心法): 东经 {center_x:.4f}, 北纬 {center_y:.4f}")
        print(f"  收敛迭代次数: {iters}, 最终运输成本估算: {cost:.2f}")
    else:
        print("  该区域没有城市 (或未在目标定义中)。")
        cluster_iterative_centers[i] = (np.nan, np.nan)

# --- 7. 单独的迭代重心法 (Unchanged) ---
print("\n--- 总配送中心 (单独使用迭代重心法, 不分区域) ---")
overall_center_x_iter, overall_center_y_iter, overall_cost_iter, overall_iters = iterative_center_of_gravity(df.copy())
print(f"配送中心: 东经 {overall_center_x_iter:.4f}, 北纬 {overall_center_y_iter:.4f}")
print(f"  收敛迭代次数: {overall_iters}, 最终运输成本估算: {overall_cost_iter:.2f}")


# --- 8. 可视化 (Unchanged in logic, will use the manually defined clusters) ---
plt.figure(figsize=(18, 14)) # Increased size for better label visibility
colors_map = matplotlib.colormaps.get_cmap('tab10').resampled(K_chosen)

# 绘制城市点 (using manually defined df['Cluster'])
for i in range(K_chosen):
    cluster_data = df[df['Cluster'] == i]
    plt.scatter(cluster_data['东经(x)'], cluster_data['北纬(y)'],
                s=cluster_data['运输量(万吨)']/100 + 30, # Adjusted size
                color=colors_map(i), label=f'区域 {i+1} 城市', alpha=0.7)
    for _, row in cluster_data.iterrows():
        coord_text = f"{row['城市']}\n({row['东经(x)']:.2f}, {row['北纬(y)']:.2f})"
        plt.text(row['东经(x)'] + 0.03, row['北纬(y)'] + 0.03, coord_text, fontsize=11,
                 bbox=dict(boxstyle="round,pad=0.2", facecolor='white', alpha=0.7, edgecolor='gray'))

# 绘制 手动定义聚类 + 迭代重心法 得到的配送中心
print("\n图例 手动定义聚类+迭代重心法 配送中心:")
for i in range(K_chosen):
    if i in cluster_iterative_centers: # Check if center was computed
        center_x, center_y = cluster_iterative_centers[i]
        if not (np.isnan(center_x) or np.isnan(center_y)):
            plt.scatter(center_x, center_y, s=300, color=colors_map(i), marker='P',
                        edgecolor='black', linewidth=1.5, label=f'区域 {i+1} DC (手动+迭代)', zorder=5)
            dc_text = f'DC {i+1}\n({center_x:.4f}, {center_y:.4f})' # Simplified label for DC
            plt.text(center_x, center_y - 0.12, dc_text, fontsize=10, weight='bold', ha='center', color='black',
                    bbox=dict(boxstyle="round,pad=0.3", facecolor=colors_map(i), alpha=0.9, edgecolor='black'))
            print(f"  区域 {i+1} DC ({colors_map(i)}): 东经 {center_x:.4f}, 北纬 {center_y:.4f}")

# 绘制 单独使用迭代重心法 得到的配送中心
if not (np.isnan(overall_center_x_iter) or np.isnan(overall_center_y_iter)):
    plt.scatter(overall_center_x_iter, overall_center_y_iter, s=400, color='magenta', marker='X',
                label='总DC (单独迭代重心法)', edgecolor='black', linewidth=1.5, zorder=6)
    total_dc_text = f'总迭代DC\n({overall_center_x_iter:.4f}, {overall_center_y_iter:.4f})'
    plt.text(overall_center_x_iter, overall_center_y_iter - 0.12, total_dc_text, fontsize=10, weight='bold', ha='center', color='black',
            bbox=dict(boxstyle="round,pad=0.3", facecolor='magenta', alpha=0.9, edgecolor='black'))
    print(f"\n图例 单独迭代重心法 总配送中心 (洋红色 X): 东经 {overall_center_x_iter:.4f}, 北纬 {overall_center_y_iter:.4f}")


plt.title(f'城市聚类 (手动定义, K={K_chosen}) 及迭代重心法配送中心', fontsize=16)
plt.xlabel('东经(x)', fontsize=14)
plt.ylabel('北纬(y)', fontsize=14)
plt.legend(loc='center left', bbox_to_anchor=(1.01, 0.5), borderaxespad=0., fontsize=10)
plt.grid(True, linestyle='--', alpha=0.7)
plt.subplots_adjust(right=0.72) # Adjust for legend
plt.savefig('clustering_result_manual.png', dpi=300, bbox_inches='tight')
print(f"\n手动聚类结果图表已保存为 'clustering_result_manual.png'")
plt.close()

print("\n--- 总结 (基于手动定义的聚类) ---")
print("手动定义聚类 + 迭代重心法 得到的各区域配送中心:")
for i in range(K_chosen):
    if i in cluster_iterative_centers:
        print(f"区域 {i+1}: 东经 {cluster_iterative_centers[i][0]:.4f}, 北纬 {cluster_iterative_centers[i][1]:.4f}")
print("\n单独使用迭代重心法得到的总配送中心:")
print(f"总计: 东经 {overall_center_x_iter:.4f}, 北纬 {overall_center_y_iter:.4f}")