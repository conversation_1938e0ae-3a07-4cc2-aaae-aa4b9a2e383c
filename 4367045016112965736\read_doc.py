#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
二维斜碰建模 - 扩展原有的一维正碰模型
包含完全弹性、非弹性、完全非弹性三种碰撞类型
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from IPython.display import HTML
import math

class ObliqueCollisionSimulator:
    """二维斜碰模拟器"""

    def __init__(self, m1=1.0, m2=1.0, dt=0.01, total_time=3.0):
        """
        初始化模拟器

        参数:
        m1, m2: 两个质点的质量
        dt: 时间步长
        total_time: 总模拟时间
        """
        self.m1 = m1
        self.m2 = m2
        self.dt = dt
        self.total_time = total_time
        self.num_steps = int(total_time / dt)

        # 质点半径（用于碰撞检测）
        self.radius = 0.05

    def set_initial_conditions(self, x1, y1, vx1, vy1, x2, y2, vx2, vy2):
        """
        设置初始条件

        参数:
        x1, y1: 质点1的初始位置
        vx1, vy1: 质点1的初始速度
        x2, y2: 质点2的初始位置
        vx2, vy2: 质点2的初始速度
        """
        self.x1, self.y1 = x1, y1
        self.vx1, self.vy1 = vx1, vy1
        self.x2, self.y2 = x2, y2
        self.vx2, self.vy2 = vx2, vy2

        # 初始化存储数组
        self.x1_vals = np.zeros(self.num_steps)
        self.y1_vals = np.zeros(self.num_steps)
        self.x2_vals = np.zeros(self.num_steps)
        self.y2_vals = np.zeros(self.num_steps)
        self.vx1_vals = np.zeros(self.num_steps)
        self.vy1_vals = np.zeros(self.num_steps)
        self.vx2_vals = np.zeros(self.num_steps)
        self.vy2_vals = np.zeros(self.num_steps)

    def check_collision(self):
        """检测两质点是否发生碰撞"""
        distance = math.sqrt((self.x1 - self.x2)**2 + (self.y1 - self.y2)**2)
        return distance <= 2 * self.radius

    def elastic_collision(self):
        """完全弹性斜碰计算"""
        # 计算连心线方向（从质点2指向质点1）
        dx = self.x1 - self.x2
        dy = self.y1 - self.y2
        distance = math.sqrt(dx**2 + dy**2)

        if distance == 0:
            return  # 避免除零错误

        # 连心线单位向量（法向）
        nx = dx / distance
        ny = dy / distance

        # 切向单位向量
        tx = -ny
        ty = nx

        # 将速度分解为法向和切向分量
        # 质点1
        v1n = self.vx1 * nx + self.vy1 * ny  # 法向分量
        v1t = self.vx1 * tx + self.vy1 * ty  # 切向分量

        # 质点2
        v2n = self.vx2 * nx + self.vy2 * ny  # 法向分量
        v2t = self.vx2 * tx + self.vy2 * ty  # 切向分量

        # 对法向分量应用一维弹性碰撞公式
        v1n_new = ((self.m1 - self.m2) * v1n + 2 * self.m2 * v2n) / (self.m1 + self.m2)
        v2n_new = (2 * self.m1 * v1n + (self.m2 - self.m1) * v2n) / (self.m1 + self.m2)

        # 切向分量保持不变
        v1t_new = v1t
        v2t_new = v2t

        # 将法向和切向分量合成为新的速度向量
        self.vx1 = v1n_new * nx + v1t_new * tx
        self.vy1 = v1n_new * ny + v1t_new * ty
        self.vx2 = v2n_new * nx + v2t_new * tx
        self.vy2 = v2n_new * ny + v2t_new * ty

    def inelastic_collision(self, e=0.8):
        """非弹性斜碰计算"""
        # 计算连心线方向（从质点2指向质点1）
        dx = self.x1 - self.x2
        dy = self.y1 - self.y2
        distance = math.sqrt(dx**2 + dy**2)

        if distance == 0:
            return  # 避免除零错误

        # 连心线单位向量（法向）
        nx = dx / distance
        ny = dy / distance

        # 切向单位向量
        tx = -ny
        ty = nx

        # 将速度分解为法向和切向分量
        # 质点1
        v1n = self.vx1 * nx + self.vy1 * ny  # 法向分量
        v1t = self.vx1 * tx + self.vy1 * ty  # 切向分量

        # 质点2
        v2n = self.vx2 * nx + self.vy2 * ny  # 法向分量
        v2t = self.vx2 * tx + self.vy2 * ty  # 切向分量

        # 对法向分量应用一维非弹性碰撞公式（乘以恢复系数e）
        v1n_new = ((self.m1 - self.m2) * v1n + 2 * self.m2 * v2n) / (self.m1 + self.m2) * e
        v2n_new = (2 * self.m1 * v1n + (self.m2 - self.m1) * v2n) / (self.m1 + self.m2) * e

        # 切向分量保持不变
        v1t_new = v1t
        v2t_new = v2t

        # 将法向和切向分量合成为新的速度向量
        self.vx1 = v1n_new * nx + v1t_new * tx
        self.vy1 = v1n_new * ny + v1t_new * ty
        self.vx2 = v2n_new * nx + v2t_new * tx
        self.vy2 = v2n_new * ny + v2t_new * ty

    def completely_inelastic_collision(self):
        """完全非弹性斜碰计算"""
        # 计算连心线方向（从质点2指向质点1）
        dx = self.x1 - self.x2
        dy = self.y1 - self.y2
        distance = math.sqrt(dx**2 + dy**2)

        if distance == 0:
            return  # 避免除零错误

        # 连心线单位向量（法向）
        nx = dx / distance
        ny = dy / distance

        # 切向单位向量
        tx = -ny
        ty = nx

        # 将速度分解为法向和切向分量
        # 质点1
        v1n = self.vx1 * nx + self.vy1 * ny  # 法向分量
        v1t = self.vx1 * tx + self.vy1 * ty  # 切向分量

        # 质点2
        v2n = self.vx2 * nx + self.vy2 * ny  # 法向分量
        v2t = self.vx2 * tx + self.vy2 * ty  # 切向分量

        # 对法向分量应用完全非弹性碰撞公式（碰撞后法向速度相同）
        vn_combined = (self.m1 * v1n + self.m2 * v2n) / (self.m1 + self.m2)

        # 切向分量保持不变
        v1t_new = v1t
        v2t_new = v2t

        # 将法向和切向分量合成为新的速度向量
        self.vx1 = vn_combined * nx + v1t_new * tx
        self.vy1 = vn_combined * ny + v1t_new * ty
        self.vx2 = vn_combined * nx + v2t_new * tx
        self.vy2 = vn_combined * ny + v2t_new * ty

    def simulate(self, collision_type='elastic', e=0.8):
        """
        运行模拟

        参数:
        collision_type: 碰撞类型 ('elastic', 'inelastic', 'completely_inelastic')
        e: 恢复系数（仅用于非弹性碰撞）
        """
        collision_occurred = False

        for i in range(self.num_steps):
            # 检测碰撞
            if self.check_collision() and not collision_occurred:
                collision_occurred = True
                if collision_type == 'elastic':
                    self.elastic_collision()
                elif collision_type == 'inelastic':
                    self.inelastic_collision(e)
                elif collision_type == 'completely_inelastic':
                    self.completely_inelastic_collision()

            # 更新位置
            self.x1 += self.vx1 * self.dt
            self.y1 += self.vy1 * self.dt
            self.x2 += self.vx2 * self.dt
            self.y2 += self.vy2 * self.dt

            # 存储当前状态
            self.x1_vals[i] = self.x1
            self.y1_vals[i] = self.y1
            self.x2_vals[i] = self.x2
            self.y2_vals[i] = self.y2
            self.vx1_vals[i] = self.vx1
            self.vy1_vals[i] = self.vy1
            self.vx2_vals[i] = self.vx2
            self.vy2_vals[i] = self.vy2

    def create_animation(self, title="二维斜碰模拟"):
        """创建动画"""
        fig, ax = plt.subplots(figsize=(10, 8))

        # 设置坐标轴范围
        x_min = min(np.min(self.x1_vals), np.min(self.x2_vals)) - 0.5
        x_max = max(np.max(self.x1_vals), np.max(self.x2_vals)) + 0.5
        y_min = min(np.min(self.y1_vals), np.min(self.y2_vals)) - 0.5
        y_max = max(np.max(self.y1_vals), np.max(self.y2_vals)) + 0.5

        ax.set_xlim(x_min, x_max)
        ax.set_ylim(y_min, y_max)
        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)
        ax.set_xlabel('X 位置')
        ax.set_ylabel('Y 位置')
        ax.set_title(title)

        # 创建质点
        atom1, = ax.plot([], [], 'bo', ms=15, label='质点1')
        atom2, = ax.plot([], [], 'ro', ms=15, label='质点2')

        # 创建轨迹线
        trail1, = ax.plot([], [], 'b-', alpha=0.3, linewidth=1)
        trail2, = ax.plot([], [], 'r-', alpha=0.3, linewidth=1)

        # 添加图例
        ax.legend()

        def init():
            atom1.set_data([], [])
            atom2.set_data([], [])
            trail1.set_data([], [])
            trail2.set_data([], [])
            return atom1, atom2, trail1, trail2

        def update(frame):
            # 更新质点位置
            atom1.set_data(self.x1_vals[frame], self.y1_vals[frame])
            atom2.set_data(self.x2_vals[frame], self.y2_vals[frame])

            # 更新轨迹（显示前面的轨迹）
            trail_length = min(frame + 1, 50)  # 显示最近50帧的轨迹
            start_idx = max(0, frame + 1 - trail_length)
            trail1.set_data(self.x1_vals[start_idx:frame+1], self.y1_vals[start_idx:frame+1])
            trail2.set_data(self.x2_vals[start_idx:frame+1], self.y2_vals[start_idx:frame+1])

            return atom1, atom2, trail1, trail2

        ani = animation.FuncAnimation(fig, update, frames=self.num_steps,
                                    init_func=init, blit=True, interval=50)
        return ani

def create_comparison_animation():
    """创建三种碰撞类型的对比动画"""
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))

    # 相同的初始条件
    initial_conditions = {
        'x1': 0.0, 'y1': 0.0, 'vx1': 1.0, 'vy1': 0.5,
        'x2': 1.5, 'y2': 0.3, 'vx2': -0.8, 'vy2': -0.2
    }

    collision_types = ['elastic', 'inelastic', 'completely_inelastic']
    titles = ['完全弹性碰撞', '非弹性碰撞 (e=0.6)', '完全非弹性碰撞']

    simulators = []
    animations_data = []

    for i, (collision_type, title) in enumerate(zip(collision_types, titles)):
        # 创建模拟器
        sim = ObliqueCollisionSimulator()
        sim.set_initial_conditions(**initial_conditions)

        # 运行模拟
        if collision_type == 'inelastic':
            sim.simulate(collision_type, e=0.6)
        else:
            sim.simulate(collision_type)

        simulators.append(sim)

        # 设置子图
        ax = axes[i]
        x_min = min(np.min(sim.x1_vals), np.min(sim.x2_vals)) - 0.3
        x_max = max(np.max(sim.x1_vals), np.max(sim.x2_vals)) + 0.3
        y_min = min(np.min(sim.y1_vals), np.min(sim.y2_vals)) - 0.3
        y_max = max(np.max(sim.y1_vals), np.max(sim.y2_vals)) + 0.3

        ax.set_xlim(x_min, x_max)
        ax.set_ylim(y_min, y_max)
        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)
        ax.set_xlabel('X 位置')
        ax.set_ylabel('Y 位置')
        ax.set_title(title)

        # 创建质点和轨迹
        atom1, = ax.plot([], [], 'bo', ms=12, label='质点1')
        atom2, = ax.plot([], [], 'ro', ms=12, label='质点2')
        trail1, = ax.plot([], [], 'b-', alpha=0.3, linewidth=1)
        trail2, = ax.plot([], [], 'r-', alpha=0.3, linewidth=1)

        if i == 0:  # 只在第一个子图显示图例
            ax.legend()

        animations_data.append((atom1, atom2, trail1, trail2, sim))

    def init():
        elements = []
        for atom1, atom2, trail1, trail2, _ in animations_data:
            atom1.set_data([], [])
            atom2.set_data([], [])
            trail1.set_data([], [])
            trail2.set_data([], [])
            elements.extend([atom1, atom2, trail1, trail2])
        return elements

    def update(frame):
        elements = []
        for atom1, atom2, trail1, trail2, sim in animations_data:
            # 更新质点位置
            atom1.set_data(sim.x1_vals[frame], sim.y1_vals[frame])
            atom2.set_data(sim.x2_vals[frame], sim.y2_vals[frame])

            # 更新轨迹
            trail_length = min(frame + 1, 30)
            start_idx = max(0, frame + 1 - trail_length)
            trail1.set_data(sim.x1_vals[start_idx:frame+1], sim.y1_vals[start_idx:frame+1])
            trail2.set_data(sim.x2_vals[start_idx:frame+1], sim.y2_vals[start_idx:frame+1])

            elements.extend([atom1, atom2, trail1, trail2])
        return elements

    ani = animation.FuncAnimation(fig, update, frames=simulators[0].num_steps,
                                init_func=init, blit=True, interval=50)
    plt.tight_layout()
    return ani

# 示例使用代码
def demo_oblique_collision():
    """演示斜碰模拟的使用"""
    print("=== 二维斜碰建模演示 ===")
    print()

    # 创建模拟器
    sim = ObliqueCollisionSimulator(m1=1.0, m2=1.5, dt=0.01, total_time=3.0)

    # 设置初始条件（斜碰场景）
    print("初始条件:")
    print("质点1: 位置(0, 0), 速度(1.2, 0.8), 质量=1.0")
    print("质点2: 位置(1.8, 0.5), 速度(-0.9, -0.3), 质量=1.5")
    print()

    sim.set_initial_conditions(
        x1=0.0, y1=0.0, vx1=1.2, vy1=0.8,
        x2=1.8, y2=0.5, vx2=-0.9, vy2=-0.3
    )

    # 运行完全弹性碰撞模拟
    print("运行完全弹性碰撞模拟...")
    sim.simulate('elastic')

    # 创建动画
    ani = sim.create_animation("完全弹性斜碰演示")

    return ani

if __name__ == "__main__":
    # 运行演示
    demo_ani = demo_oblique_collision()

    # 创建对比动画
    print("\n创建三种碰撞类型的对比动画...")
    comparison_ani = create_comparison_animation()

    # 在Jupyter中显示动画
    print("\n在Jupyter Notebook中使用以下代码显示动画:")
    print("HTML(demo_ani.to_jshtml())")
    print("HTML(comparison_ani.to_jshtml())")
